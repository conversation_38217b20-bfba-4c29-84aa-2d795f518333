/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #0f172a;
    background-color: #ffffff;
    overflow-x: hidden;
    font-size: 16px;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #0f172a;
}

h1 {
    font-size: 4rem;
    font-weight: 900;
    letter-spacing: -0.02em;
}

h2 {
    font-size: 3rem;
    font-weight: 800;
    letter-spacing: -0.02em;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

p {
    margin-bottom: 1rem;
    color: #64748b;
    font-size: 1.125rem;
    line-height: 1.7;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(255, 107, 53, 0.25);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(255, 107, 53, 0.35);
}

.btn-secondary {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 107, 53, 0.15);
    border-color: rgba(255, 107, 53, 0.3);
}

.btn i {
    font-size: 14px;
}

/* Badges */
.hero-badge, .section-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.badge-text {
    position: relative;
}

.badge-text::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #ff6b35;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 40px;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    font-size: 15px;
    transition: color 0.2s ease;
    position: relative;
}

.nav-links a:hover {
    color: #0f172a;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: #ff6b35;
    transition: width 0.2s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.demo-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
    color: white !important;
    padding: 12px 24px !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 14px 0 rgba(255, 107, 53, 0.25) !important;
}

.demo-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px 0 rgba(255, 107, 53, 0.35) !important;
}

.demo-btn::after {
    display: none !important;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: #64748b;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    padding: 140px 0 100px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    margin-bottom: 24px;
    color: #0f172a;
    line-height: 1.1;
}

.gradient-text {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 40px;
    color: #64748b;
    line-height: 1.7;
    max-width: 90%;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 60px;
}

.hero-stats {
    display: flex;
    gap: 48px;
}

.stat-item {
    text-align: left;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #0f172a;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

/* Hero Visual - Dashboard Mockup */
.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dashboard-mockup {
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.mockup-header {
    background: #f8fafc;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.mockup-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ef4444; }
.control.yellow { background: #f59e0b; }
.control.green { background: #10b981; }

.mockup-title {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
}

.mockup-content {
    padding: 24px;
}

.analytics-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #0f172a;
    margin: 0;
}

.trend-up {
    background: #dcfce7;
    color: #166534;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.chart-placeholder {
    height: 80px;
    display: flex;
    align-items: end;
    justify-content: center;
}

.chart-bars {
    display: flex;
    gap: 8px;
    align-items: end;
    height: 100%;
}

.bar {
    width: 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 4px 4px 0 0;
    animation: growBar 2s ease-out;
}

.feature-cards {
    display: flex;
    gap: 12px;
}

.feature-card {
    flex: 1;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 16px;
}

.feature-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    margin: 0 auto 8px;
}

.ai-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.analytics-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.feature-card span {
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
}

@keyframes growBar {
    from { height: 0; }
    to { height: var(--height, 60%); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Section Styles */
section {
    padding: 120px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    margin-bottom: 24px;
    color: #0f172a;
}

.section-header p {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.7;
}

/* Features Section */
.features {
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 32px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.feature-card.large {
    grid-row: span 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.feature-icon-wrapper {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.feature-icon {
    width: 28px;
    height: 28px;
    border-radius: 8px;
}

.brain-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.analytics-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.integration-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 16px;
}

.feature-card p {
    color: #64748b;
    margin-bottom: 24px;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0 0 32px 0;
}

.feature-list li {
    padding: 8px 0;
    color: #64748b;
    position: relative;
    padding-left: 24px;
}

.feature-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 16px;
    width: 6px;
    height: 6px;
    background: #ff6b35;
    border-radius: 50%;
}

.feature-visual {
    margin-top: auto;
}

.ai-visualization {
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
}

.neural-network {
    position: relative;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.node {
    width: 12px;
    height: 12px;
    background: #e2e8f0;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.node.active {
    background: #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
    animation: pulse 2s infinite;
}

.connection {
    position: absolute;
    height: 2px;
    background: #e2e8f0;
    width: 20px;
    transition: all 0.3s ease;
}

.connection.active {
    background: #ff6b35;
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.mini-chart {
    height: 40px;
    background: #f8fafc;
    border-radius: 8px;
    margin-top: 16px;
    position: relative;
    overflow: hidden;
}

.chart-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.2), rgba(247, 147, 30, 0.2));
    border-radius: 8px 8px 0 0;
    animation: chartGrow 2s ease-out;
}

.integration-visual {
    margin-top: 16px;
}

.api-blocks {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.api-block {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 6px;
    animation: blockFloat 3s ease-in-out infinite;
}

.api-block:nth-child(2) {
    animation-delay: 0.5s;
}

.api-block:nth-child(3) {
    animation-delay: 1s;
}

@keyframes chartGrow {
    from { height: 0; }
    to { height: 60%; }
}

@keyframes blockFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
}

/* About Section */
.about {
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    font-size: 1.125rem;
    margin-bottom: 30px;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feature i {
    color: #10b981;
    font-size: 18px;
}

.feature span {
    color: #374151;
    font-weight: 500;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: #4f46e5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #4f46e5;
}

.timeline-year {
    font-size: 1.25rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 5px;
}

.timeline-content {
    color: #6b7280;
    font-weight: 500;
}

/* Products Section */
.products {
    background: #f8fafc;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    max-width: 1200px;
    margin: 0 auto;
}

.product-card {
    background: white;
    border-radius: 24px;
    padding: 32px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #ff6b35;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
}

.product-icon-wrapper {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.product-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.book-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.publisher-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.platform-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.product-badge {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.product-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 8px;
}

.product-subtitle {
    color: #ff6b35;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 16px !important;
}

.product-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 24px;
    flex: 1;
}

.product-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.feature-icon-small {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    flex-shrink: 0;
}

.ai-small {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.personalized-small, .analytics-small {
    background: linear-gradient(135deg, #10b981, #059669);
}

.interactive-small, .brand-small {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.platform-small, .enterprise-small {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.feature-highlight span {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.trusted-publishers {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.trusted-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
    margin-bottom: 8px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.publisher-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.publisher-tag {
    background: white;
    color: #64748b;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e2e8f0;
}

.product-cta {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin-top: auto;
}

.product-cta:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(255, 107, 53, 0.35);
}

.product-cta i {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.product-cta:hover i {
    transform: translateX(2px);
}

/* Why It Matters Section */
.why-matters {
    background: #f8fafc;
}

.stakeholders-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.stakeholder-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stakeholder-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stakeholder-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.stakeholder-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 15px;
}

.stakeholder-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Integration Section */
.integration {
    background: white;
}

.integration-content {
    max-width: 1000px;
    margin: 0 auto;
}

.integration-benefits {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    background: #f8fafc;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.benefit-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.benefit-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.benefit-text p {
    color: #6b7280;
    margin: 0;
}

.integration-cta {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 20px;
    color: white;
}

.integration-cta h3 {
    font-size: 1.75rem;
    margin-bottom: 20px;
    color: white;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.cta-buttons .btn {
    background: white;
    color: #4f46e5;
    border: 2px solid white;
}

.cta-buttons .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.cta-content {
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #1f2937;
}

.cta-content > p {
    font-size: 1.25rem;
    margin-bottom: 50px;
    color: #6b7280;
}

.cta-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.cta-option {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.cta-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.cta-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.cta-option h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 10px;
}

.cta-option p {
    color: #6b7280;
    margin-bottom: 25px;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 20px;
}

.footer-logo-img {
    height: 60px;
    width: auto;
    object-fit: contain;
}

.footer-logo .logo-icon {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.footer-description {
    color: #9ca3af;
    margin-bottom: 25px;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #ff6b35;
    transform: translateY(-2px);
}

.footer-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff6b35;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

/* Welcome Banner */
.welcome-banner {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 15px 25px;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
    z-index: 1000;
    animation: slideIn 0.5s ease-out;
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.welcome-icon {
    font-size: 20px;
}

.welcome-text {
    font-weight: 600;
    font-size: 14px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 20px;
    }

    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .hero-visual {
        height: 500px;
    }

    .dashboard-mockup {
        max-width: 400px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card.large {
        grid-row: span 1;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr 1fr;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
    }

    .cta-options {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero {
        padding: 120px 0 80px;
        min-height: auto;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
        gap: 32px;
    }

    .hero-visual {
        height: 400px;
    }

    .dashboard-mockup {
        max-width: 320px;
    }

    .mockup-content {
        padding: 16px;
    }

    .analytics-card {
        padding: 16px;
    }

    section {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .feature-card {
        padding: 24px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-card {
        padding: 24px;
    }

    .product-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stakeholder-card {
        padding: 24px;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .benefit-item {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .integration-cta {
        padding: 24px;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .cta-options {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .cta-option {
        padding: 24px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .welcome-banner {
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
    }

    .welcome-text {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    .hero {
        padding: 90px 0 50px;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .floating-card {
        min-width: 140px;
        padding: 12px;
    }

    .product-card {
        padding: 25px 15px;
    }

    .stakeholder-card {
        padding: 25px 15px;
    }

    .stakeholder-icon {
        font-size: 36px;
    }

    .benefit-item {
        padding: 15px;
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .integration-cta {
        padding: 25px 15px;
    }

    .cta-option {
        padding: 25px 15px;
    }

    .cta-icon {
        font-size: 36px;
    }

    .welcome-banner {
        bottom: 15px;
        right: 15px;
        padding: 10px 15px;
    }

    .welcome-icon {
        font-size: 16px;
    }

    .welcome-text {
        font-size: 11px;
    }
}
